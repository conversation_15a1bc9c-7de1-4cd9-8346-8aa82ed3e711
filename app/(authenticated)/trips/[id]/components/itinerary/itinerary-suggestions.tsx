"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { useToast } from "@/components/ui/use-toast"
import { useUserWithData } from "@/lib/domains/auth/auth.hooks"
import { useRealtimeUserAIUsage } from "@/lib/domains/user-ai-usage/user-ai-usage.realtime.hooks"
import { AIUsageCategory } from "@/lib/domains/user-ai-usage/user-ai-usage.types"
import { useIsUserSubscribed } from "@/lib/domains/user-subscription/user-subscription.hooks"
import { SubscriptionErrorType } from "@/lib/subscription-errors"
import { AIUsageWarning } from "@/components/ai-usage-warning"
import { Spark<PERSON>, Plus, ExternalLink, Clock, RefreshCw } from "lucide-react"
import { type ItinerarySuggestion } from "@/lib/openai"
import { type Trip } from "@/lib/domains/trip/trip.types"
import { ItineraryItem } from "@/lib/domains/itinerary/itinerary.types"
import { useAIItinerarySuggestions } from "@/lib/domains/ai-suggestions/ai-suggestions-itinerary.hooks"

interface ItinerarySuggestionsProps {
  trip: Trip
  day: number
  existingActivities: ItineraryItem[]
  onActivitySelected: (activity: ItinerarySuggestion) => void
}

export function ItinerarySuggestions({
  trip,
  day,
  existingActivities,
  onActivitySelected,
}: ItinerarySuggestionsProps) {
  const { userData, loading: userDataLoading } = useUserWithData()
  const { toast } = useToast()
  const isSubscribed = useIsUserSubscribed()
  const { getCategoryUsage } = useRealtimeUserAIUsage(isSubscribed)

  // Use the new AI itinerary suggestions hook
  const {
    suggestions,
    loading,
    error,
    usageError,
    showSuggestions,
    usingCachedSuggestions,
    affiliateLinks,
    loadSuggestions,
  } = useAIItinerarySuggestions(trip, day, existingActivities, userData)

  const handleSelectActivity = (suggestion: ItinerarySuggestion) => {
    onActivitySelected(suggestion)

    toast({
      title: "Activity selected",
      description: `"${suggestion.title}" has been added to your itinerary`,
    })
  }

  if (!showSuggestions) {
    return (
      <>
        {usageError && usageError.show && (
          <AIUsageWarning
            errorType={usageError.errorType}
            usageData={usageError.usageData}
            onClose={() => {}}
            className="mb-4"
          />
        )}
        <Button
          variant="outline"
          className="w-full"
          onClick={() => loadSuggestions(false)}
          disabled={loading || userDataLoading}
        >
          {loading ? (
            <>
              <div className="flex items-center justify-center gap-2">
                <RefreshCw className="h-4 w-4 animate-spin" />
                <span className="text-xs sm:text-sm">Loading suggestions...</span>
              </div>
            </>
          ) : (
            <>
              <Sparkles className="h-4 w-4 mr-2" />
              {!isSubscribed && (
                <span className="mr-2 text-xs">
                  {getCategoryUsage(AIUsageCategory.ITINERARY)?.count || 0}/
                  {getCategoryUsage(AIUsageCategory.ITINERARY)?.limit || 3}
                </span>
              )}
              Get AI Activity Suggestions
            </>
          )}
        </Button>
      </>
    )
  }

  if (loading) {
    return (
      <>
        {usageError && usageError.show && (
          <AIUsageWarning
            errorType={usageError.errorType}
            usageData={usageError.usageData}
            onClose={() => {}}
            className="mb-4"
          />
        )}
        <Card id="ai-activities-suggestions">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Sparkles className="h-5 w-5" />
              <Skeleton className="h-6 w-48" />
            </CardTitle>
            <CardDescription>
              <Skeleton className="h-4 w-full" />
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="space-y-2">
                <Skeleton className="h-5 w-3/4" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-2/3" />
              </div>
            ))}
          </CardContent>
        </Card>
      </>
    )
  }

  if (suggestions.length === 0) {
    return (
      <>
        {usageError && usageError.show && (
          <AIUsageWarning
            errorType={usageError.errorType}
            usageData={usageError.usageData}
            onClose={() => {}}
            className="mb-4"
          />
        )}
        <Card id="ai-activities-suggestions">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Sparkles className="h-5 w-5" />
              No AI Activities Available
            </CardTitle>
            <CardDescription>
              We couldn't find any new activity suggestions for this day
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">
              Try adding some activities manually or check back later
            </p>
            <Button
              variant="outline"
              className="w-full"
              onClick={() => loadSuggestions(true)}
              disabled={loading}
            >
              {loading ? (
                <div className="flex items-center justify-center gap-2">
                  <RefreshCw className="h-4 w-4 animate-spin" />
                  <span>Loading...</span>
                </div>
              ) : (
                <>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  {!isSubscribed && (
                    <span className="mr-2 text-xs">
                      {getCategoryUsage(AIUsageCategory.ITINERARY)?.count || 0}/
                      {getCategoryUsage(AIUsageCategory.ITINERARY)?.limit || 3}
                    </span>
                  )}
                  Try Again
                </>
              )}
            </Button>
          </CardContent>
        </Card>
      </>
    )
  }

  return (
    <>
      {usageError && usageError.show && (
        <AIUsageWarning
          errorType={usageError.errorType}
          usageData={usageError.usageData}
          onClose={() => {}}
          className="mb-4"
        />
      )}
      <Card id="ai-activities-suggestions">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="h-5 w-5" />
            {usingCachedSuggestions ? "Cached AI Activities" : "AI Activities Suggestions"}
            <div className="flex items-center ml-auto gap-2">
              {!isSubscribed && (
                <span className="text-xs text-muted-foreground">
                  {getCategoryUsage(AIUsageCategory.ITINERARY)?.count || 0}/
                  {getCategoryUsage(AIUsageCategory.ITINERARY)?.limit || 3}
                </span>
              )}
              {usingCachedSuggestions && (
                <Badge variant="outline" className="text-xs bg-primary/10 border-primary/20">
                  Cached
                </Badge>
              )}
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => loadSuggestions(true)}
                disabled={loading}
                title="Refresh AI suggestions"
              >
                <RefreshCw className={`h-4 w-4 ${loading ? "animate-spin" : ""}`} />
              </Button>
            </div>
          </CardTitle>
          <CardDescription>
            Here are some {usingCachedSuggestions ? "cached" : "suggested"} activities for day {day}{" "}
            of your trip to {trip.destination}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {suggestions.map((suggestion, index) => {
            const affiliateLink = affiliateLinks[suggestion.title]
            return (
              <div key={index} className="border rounded-lg p-4 space-y-2">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="font-medium">{suggestion.title}</h3>
                    <p className="text-sm text-muted-foreground">{suggestion.description}</p>
                  </div>
                  <div className="flex items-center gap-1 text-xs text-muted-foreground">
                    <Clock className="h-3 w-3" />
                    <span>{suggestion.duration || "1-2 hours"}</span>
                  </div>
                </div>

                {affiliateLink && (
                  <div className="bg-muted/50 p-3 rounded-md mt-2">
                    <div className="flex justify-between items-center">
                      <div>
                        <h4 className="text-sm font-medium">{affiliateLink.title}</h4>
                        <p className="text-xs text-muted-foreground">{affiliateLink.description}</p>
                      </div>
                      <Button variant="outline" size="sm" asChild>
                        <a
                          href={affiliateLink.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center gap-1"
                        >
                          <span>{(affiliateLink as any).provider || "Visit"}</span>
                          <ExternalLink className="h-3 w-3" />
                        </a>
                      </Button>
                    </div>
                  </div>
                )}

                <Button
                  variant="outline"
                  size="sm"
                  className="w-full mt-2"
                  onClick={() => handleSelectActivity(suggestion)}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add to Itinerary
                </Button>
              </div>
            )
          })}

          <Button
            variant="outline"
            className="w-full"
            onClick={() => loadSuggestions(true)}
            disabled={loading}
          >
            {loading ? (
              <div className="flex items-center justify-center gap-2">
                <RefreshCw className="h-4 w-4 animate-spin" />
                <span>Loading...</span>
              </div>
            ) : (
              <>
                <RefreshCw className="h-4 w-4 mr-2" />
                {!isSubscribed && (
                  <span className="mr-2 text-xs">
                    {getCategoryUsage(AIUsageCategory.ITINERARY)?.count || 0}/
                    {getCategoryUsage(AIUsageCategory.ITINERARY)?.limit || 3}
                  </span>
                )}
                Get New Suggestions
              </>
            )}
          </Button>
        </CardContent>
      </Card>
    </>
  )
}
