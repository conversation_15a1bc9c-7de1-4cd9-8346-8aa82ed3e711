"use client"

import { useState, useEffect, useRef } from "react"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Loader2 } from "lucide-react"
import { auth } from "@/lib/firebase"

interface Prediction {
  place_id: string
  description: string
}

interface DestinationAutocompleteProps {
  value: string
  onChange: (value: string, placeId?: string) => void
  placeholder?: string
  required?: boolean
  allowUnauthenticated?: boolean
}

export function DestinationAutocomplete({
  value,
  onChange,
  placeholder = "Enter a destination",
  required = false,
  allowUnauthenticated = false,
}: DestinationAutocompleteProps) {
  const [predictions, setPredictions] = useState<Prediction[]>([])
  const [loading, setLoading] = useState(false)
  const [showPredictions, setShowPredictions] = useState(false)
  const inputRef = useRef<HTMLInputElement>(null)

  // Helper function to get the authentication token
  const getAuthToken = async () => {
    try {
      const currentUser = auth.currentUser
      if (!currentUser) {
        throw new Error("User not authenticated")
      }

      const token = await currentUser.getIdToken()
      return token
    } catch (error) {
      console.error("Error getting auth token:", error)
      throw error
    }
  }

  // Get predictions when input value changes
  useEffect(() => {
    if (!value || value.length < 3) {
      setPredictions([])
      return
    }

    const getPredictions = async () => {
      setLoading(true)
      try {
        let response: Response

        if (allowUnauthenticated) {
          // Make unauthenticated request to public endpoint
          response = await fetch(
            `/api/places/autocomplete-public?query=${encodeURIComponent(value)}`
          )
        } else {
          // Get authentication token
          const token = await getAuthToken()

          // Make authenticated request
          response = await fetch(`/api/places/autocomplete?query=${encodeURIComponent(value)}`, {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          })
        }

        if (!response.ok) {
          throw new Error(`API error: ${response.statusText}`)
        }

        const data = await response.json()

        if (data.predictions) {
          setPredictions(data.predictions)
          setShowPredictions(true)
        } else {
          setPredictions([])
        }
      } catch (error: any) {
        console.error("Error getting place predictions:", error)
        setPredictions([])

        // Check if it's an authentication error (only for authenticated requests)
        if (!allowUnauthenticated && error.message?.includes("Unauthorized")) {
          console.error(
            "Authentication error when fetching place predictions. Make sure you're logged in."
          )
        }
      } finally {
        setLoading(false)
      }
    }

    const timeoutId = setTimeout(() => {
      getPredictions()
    }, 300)

    return () => clearTimeout(timeoutId)
  }, [value])

  // Handle click outside to close predictions
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (inputRef.current && !inputRef.current.contains(event.target as Node)) {
        setShowPredictions(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])

  return (
    <div className="relative" ref={inputRef}>
      <Label htmlFor="destination">Destination</Label>
      <div className="relative">
        <Input
          id="destination"
          value={value}
          onChange={(e) => onChange(e.target.value, undefined)}
          placeholder={placeholder}
          required={required}
          onFocus={() => value && predictions.length > 0 && setShowPredictions(true)}
        />
        {loading && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
            <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
          </div>
        )}
      </div>

      {showPredictions && predictions.length > 0 && (
        <div className="absolute z-10 mt-1 w-full bg-background border rounded-md shadow-lg max-h-60 overflow-auto">
          {predictions.map((prediction) => (
            <div
              key={prediction.place_id}
              className="px-4 py-2 hover:bg-muted cursor-pointer"
              onClick={() => {
                onChange(prediction.description, prediction.place_id)
                setShowPredictions(false)
              }}
            >
              {prediction.description}
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
